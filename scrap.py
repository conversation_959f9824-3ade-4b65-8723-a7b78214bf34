#!/usr/bin/env python3
"""
Court Order Scraper for Madras High Court
Automates the process of downloading court orders from https://hcservices.ecourts.gov.in/hcservices/main.php
"""

import os
import time
import requests
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import urllib.parse
from pathlib import Path

class CourtOrderScraper:
    def __init__(self, download_dir="downloads", headless=False, captcha_wait_minutes=5):
        """
        Initialize the scraper

        Args:
            download_dir (str): Directory to save downloaded PDFs
            headless (bool): Run browser in headless mode
            captcha_wait_minutes (int): Minutes to wait for captcha input
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.base_url = "https://hcservices.ecourts.gov.in/hcservices/main.php"
        self.session = requests.Session()
        self.driver = None
        self.headless = headless
        self.captcha_wait_minutes = captcha_wait_minutes

    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")

        # Set download preferences
        prefs = {
            "download.default_directory": str(self.download_dir.absolute()),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            return True
        except Exception as e:
            print(f"Error setting up Chrome driver: {e}")
            print("Please ensure ChromeDriver is installed and in PATH")
            return False

    def wait_for_element(self, by, value, timeout=10):
        """Wait for element to be present and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            print(f"Timeout waiting for element: {by}={value}")
            return None

    def wait_for_clickable_element(self, by, value, timeout=10):
        """Wait for element to be clickable and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            print(f"Timeout waiting for clickable element: {by}={value}")
            return None

    def wait_for_page_load(self, timeout=30):
        """Wait for page to be fully loaded"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            # Additional wait for any dynamic content
            time.sleep(2)
            return True
        except TimeoutException:
            print("Page load timeout")
            return False

    def click_court_orders_menu(self):
        """Step 1: Click on Court Orders menu"""
        print("Step 1: Clicking Court Orders menu...")

        try:
            # Handle any initial modal dialogs
            self.handle_modal_dialogs()

            # Wait for page to fully load
            time.sleep(3)

            # Try multiple selectors for the Court Orders menu
            selectors = [
                (By.ID, "leftPaneMenuCO"),
                (By.CSS_SELECTOR, "li.leftPaneMenu1.activea"),
                (By.XPATH, "//div[@id='leftPaneMenuCO']"),
                (By.XPATH, "//p[contains(text(), 'Court Orders')]/parent::div"),
                (By.XPATH, "//li[contains(@class, 'leftPaneMenu1')]//p[contains(text(), 'Court Orders')]")
            ]

            for selector_type, selector_value in selectors:
                try:
                    print(f"Trying selector: {selector_type}={selector_value}")
                    court_orders_menu = self.wait_for_element(selector_type, selector_value, timeout=5)

                    if court_orders_menu:
                        # Scroll to the element
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", court_orders_menu)
                        time.sleep(1)

                        # Try different click methods
                        click_methods = [
                            lambda: self.driver.execute_script("arguments[0].click();", court_orders_menu),
                            lambda: court_orders_menu.click(),
                            lambda: self.driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", court_orders_menu)
                        ]

                        for i, click_method in enumerate(click_methods):
                            try:
                                print(f"Trying click method {i+1}...")
                                click_method()
                                time.sleep(3)
                                print("✓ Court Orders menu clicked successfully")
                                return True
                            except Exception as click_error:
                                print(f"Click method {i+1} failed: {click_error}")
                                continue

                except Exception as selector_error:
                    print(f"Selector failed: {selector_error}")
                    continue

            print("✗ Could not find or click Court Orders menu with any method")
            return False

        except Exception as e:
            print(f"✗ Error clicking Court Orders menu: {e}")
            return False

    def select_court_and_bench(self):
        """Step 2: Select Madras High Court and Principal Bench"""
        print("Step 2: Selecting Madras High Court and Principal Bench...")

        try:
            # Handle any modal dialogs first
            self.handle_modal_dialogs()

            # Select Madras High Court
            state_dropdown = self.wait_for_element(By.ID, "sess_state_code")
            if state_dropdown:
                select_state = Select(state_dropdown)
                select_state.select_by_value("10")  # Madras High Court value
                time.sleep(3)  # Wait for bench dropdown to populate
                print("✓ Madras High Court selected")
            else:
                print("✗ Could not find state dropdown")
                return False

            # Wait for bench dropdown to be populated after state selection
            time.sleep(2)

            # Select Principal Bench
            bench_dropdown = self.wait_for_element(By.ID, "court_complex_code")
            if bench_dropdown:
                # Wait for options to be available
                WebDriverWait(self.driver, 10).until(
                    lambda driver: len(Select(bench_dropdown).options) > 1
                )
                select_bench = Select(bench_dropdown)
                select_bench.select_by_value("1")  # Principal Bench value
                time.sleep(2)
                print("✓ Principal Bench selected")
                return True
            else:
                print("✗ Could not find bench dropdown")
                return False

        except Exception as e:
            print(f"✗ Error selecting court and bench: {e}")
            return False

    def handle_modal_dialogs(self):
        """Handle any modal dialogs that might be blocking interactions"""
        try:
            # Check for modal dialogs and close them
            modals = self.driver.find_elements(By.CSS_SELECTOR, ".modal.show, .modal.fade.show")
            for modal in modals:
                try:
                    # Look for close buttons in the modal
                    close_buttons = modal.find_elements(By.CSS_SELECTOR, ".btn-close, .close, button[data-dismiss='modal'], button[data-bs-dismiss='modal']")
                    if close_buttons:
                        close_buttons[0].click()
                        time.sleep(1)
                        print("✓ Closed modal dialog")
                    else:
                        # Try to click outside the modal to close it
                        self.driver.execute_script("arguments[0].style.display = 'none';", modal)
                        print("✓ Hidden modal dialog")
                except:
                    continue

            # Also check for any alert dialogs
            try:
                alert = self.driver.switch_to.alert
                alert.accept()
                print("✓ Accepted alert dialog")
            except:
                pass

        except Exception as e:
            # Not a critical error, continue
            pass

    def click_order_by_date(self):
        """Step 3: Click Order by Date option"""
        print("Step 3: Clicking Order by Date...")

        try:
            # First handle any modal dialogs that might be blocking
            self.handle_modal_dialogs()

            # Wait a bit for any animations to complete
            time.sleep(2)

            # Try to find and click the Order by Date button
            order_date_btn = self.wait_for_element(By.ID, "COorderDate")
            if order_date_btn:
                # Scroll the element into view
                self.driver.execute_script("arguments[0].scrollIntoView(true);", order_date_btn)
                time.sleep(1)

                # Try clicking with JavaScript if regular click fails
                try:
                    order_date_btn.click()
                except Exception:
                    print("Regular click failed, trying JavaScript click...")
                    self.driver.execute_script("arguments[0].click();", order_date_btn)

                time.sleep(3)  # Wait longer for date fields to appear
                print("✓ Order by Date clicked successfully")
                print("⏳ Waiting for date input fields to be ready...")
                time.sleep(2)  # Additional wait for date fields
                return True
            else:
                print("✗ Could not find Order by Date button")
                return False
        except Exception as e:
            print(f"✗ Error clicking Order by Date: {e}")
            return False

    def set_date_range(self, from_date=None, to_date=None):
        """Set the date range for searching orders"""
        print("Step 3b: Setting date range...")

        # Default to yesterday if no dates provided
        if not from_date:
            from_date = (datetime.now() - timedelta(days=1)).strftime("%d-%m-%Y")
        if not to_date:
            to_date = datetime.now().strftime("%d-%m-%Y")

        try:
            # Set from date with multiple methods
            from_date_field = self.wait_for_element(By.ID, "from_date")
            if from_date_field:
                print(f"🗓️ Setting from date to: {from_date}")

                # Ensure field is visible
                self.driver.execute_script("arguments[0].scrollIntoView(true);", from_date_field)
                time.sleep(0.5)

                # Use JavaScript to set value (keyboard input is disabled with onkeydown="return false;")
                print("🔧 Using JavaScript to bypass keyboard restriction...")
                self.driver.execute_script("""
                    var field = arguments[0];
                    var date = arguments[1];

                    // Remove readonly and disabled attributes if any
                    field.removeAttribute('readonly');
                    field.removeAttribute('disabled');

                    // Set the value
                    field.value = date;

                    // Trigger all relevant events
                    field.dispatchEvent(new Event('input', {bubbles: true}));
                    field.dispatchEvent(new Event('change', {bubbles: true}));
                    field.dispatchEvent(new Event('blur', {bubbles: true}));

                    // Also try jQuery trigger if available
                    if (typeof jQuery !== 'undefined') {
                        jQuery(field).trigger('change');
                    }
                """, from_date_field, from_date)
                time.sleep(1)

                # Final verification
                final_value = from_date_field.get_attribute("value")
                print(f"✓ From date field value: {final_value}")

                if final_value != from_date:
                    print(f"⚠️ From date verification failed. Expected: {from_date}, Got: {final_value}")
                    print("🔄 Trying one more time with direct property setting...")
                    self.driver.execute_script(f"arguments[0].value = '{from_date}';", from_date_field)
                    time.sleep(1)
            else:
                print("✗ Could not find from date field")
                return False

            # Set to date with multiple methods
            to_date_field = self.wait_for_element(By.ID, "to_date")
            if to_date_field:
                print(f"🗓️ Setting to date to: {to_date}")

                # Use JavaScript to set value (keyboard input is disabled with onkeydown="return false;")
                print("🔧 Using JavaScript to bypass keyboard restriction...")
                self.driver.execute_script("""
                    var field = arguments[0];
                    var date = arguments[1];

                    // Remove readonly and disabled attributes if any
                    field.removeAttribute('readonly');
                    field.removeAttribute('disabled');

                    // Set the value
                    field.value = date;

                    // Trigger all relevant events
                    field.dispatchEvent(new Event('input', {bubbles: true}));
                    field.dispatchEvent(new Event('change', {bubbles: true}));
                    field.dispatchEvent(new Event('blur', {bubbles: true}));

                    // Also try jQuery trigger if available
                    if (typeof jQuery !== 'undefined') {
                        jQuery(field).trigger('change');
                    }
                """, to_date_field, to_date)
                time.sleep(1)

                # Final verification
                final_value = to_date_field.get_attribute("value")
                print(f"✓ To date field value: {final_value}")

                if final_value != to_date:
                    print(f"⚠️ To date verification failed. Expected: {to_date}, Got: {final_value}")
                    print("🔄 Trying one more time with direct property setting...")
                    self.driver.execute_script(f"arguments[0].value = '{to_date}';", to_date_field)
                    time.sleep(1)

                # Final check of both dates
                from_final = from_date_field.get_attribute("value")
                to_final = to_date_field.get_attribute("value")

                print(f"📅 Final date verification - From: {from_final}, To: {to_final}")

                # Wait for page to process the date inputs
                print("⏳ Waiting for page to process date inputs...")
                time.sleep(3)

                if from_final and to_final:
                    print("✅ Date fields have values, proceeding...")
                    return True
                else:
                    print("❌ One or both date fields are empty!")
                    return False
            else:
                print("✗ Could not find to date field")
                return False

        except Exception as e:
            print(f"✗ Error setting date range: {e}")
            return False

    def handle_captcha(self, max_wait_minutes=5):
        """Step 4: Handle captcha input with extended wait time"""
        print("Step 4: Handling captcha...")

        try:
            # Wait a bit for the page to settle after date input
            print("⏳ Waiting for captcha section to load...")
            time.sleep(3)

            # Wait for captcha image to load
            captcha_img = self.wait_for_element(By.ID, "captcha_image", timeout=15)
            if not captcha_img:
                print("✗ Could not find captcha image")
                print("💡 Tip: Make sure the date fields were filled correctly")
                return False

            print("📷 Captcha image found. Please solve it manually.")
            print(f"🕐 The script will wait up to {max_wait_minutes} minutes for you to enter the captcha...")
            print("👀 Please switch to the Chrome browser window and enter the captcha")
            print("🔤 Look for the captcha image and type what you see in the 'Enter Captcha' field")
            print("📝 Note: Captcha is usually 4-6 characters. Enter the complete captcha before the script continues.")

            # Wait for user to enter captcha
            captcha_input = self.wait_for_element(By.ID, "captcha")
            if not captcha_input:
                print("✗ Could not find captcha input field")
                return False

            # Extended wait with progress indicators
            max_wait_seconds = max_wait_minutes * 60
            wait_count = 0

            print(f"⏳ Waiting for captcha input (timeout in {max_wait_minutes} minutes)...")

            while wait_count < max_wait_seconds:
                try:
                    captcha_value = captcha_input.get_attribute("value")
                    if captcha_value and len(captcha_value.strip()) >= 6:  # Wait for at least 4 characters
                        print(f"✓ Captcha entered: {captcha_value}")
                        print("⏳ Waiting 3 seconds to ensure captcha is complete...")
                        time.sleep(3)  # Wait to ensure user finished typing

                        # Check again to make sure captcha is still there and complete
                        final_captcha = captcha_input.get_attribute("value")
                        if final_captcha and len(final_captcha.strip()) >= 4:
                            print(f"✓ Final captcha confirmed: {final_captcha}")
                            return True
                        else:
                            print("⚠️ Captcha seems incomplete, continuing to wait...")
                    elif captcha_value and len(captcha_value.strip()) > 0:
                        # Show partial input but don't proceed yet
                        if wait_count % 5 == 0:  # Show every 5 seconds
                            print(f"📝 Partial captcha entered: {captcha_value} (need at least 4 characters)")


                    # Show progress every 30 seconds
                    if wait_count % 30 == 0 and wait_count > 0:
                        remaining_minutes = (max_wait_seconds - wait_count) // 60
                        remaining_seconds = (max_wait_seconds - wait_count) % 60
                        print(f"⏳ Still waiting... {remaining_minutes}m {remaining_seconds}s remaining")
                        print("💡 Tip: Switch to the Chrome browser window and enter the captcha")

                    time.sleep(1)
                    wait_count += 1

                except Exception as e:
                    print(f"Warning: Error checking captcha input: {e}")
                    time.sleep(1)
                    wait_count += 1
                    continue

            print(f"⏰ Timeout: No captcha entered within {max_wait_minutes} minutes")
            print("❌ Please try running the script again and enter the captcha more quickly")
            return False

        except Exception as e:
            print(f"✗ Error handling captcha: {e}")
            return False

    def click_go_button(self):
        """Click the Go button to submit the search"""
        print("Step 4b: Clicking Go button...")

        try:
            go_button = self.wait_for_element(By.CSS_SELECTOR, "input.Gobtn[value='Go']")
            if go_button:
                go_button.click()
                time.sleep(3)  # Wait for results to load
                print("✓ Go button clicked successfully")
                return True
            else:
                print("✗ Could not find Go button")
                return False
        except Exception as e:
            print(f"✗ Error clicking Go button: {e}")
            return False

    def extract_pdf_links(self):
        """Step 5: Extract all PDF download links from the results table"""
        print("Step 5: Extracting PDF download links...")

        try:
            # Wait for results table to load (AJAX response)
            print("⏳ Waiting for search results to load...")
            time.sleep(5)

            # Wait for AJAX results to appear
            print("🔄 Waiting for AJAX results...")
            max_wait = 30  # seconds
            wait_count = 0

            while wait_count < max_wait:
                page_text = self.driver.find_element(By.TAG_NAME, "body").text
                if "TOTAL BENCH" in page_text or "Total Number of Cases" in page_text:
                    print("✓ AJAX results loaded successfully")
                    break
                time.sleep(1)
                wait_count += 1
                if wait_count % 5 == 0:
                    print(f"⏳ Still waiting for results... ({wait_count}s)")

            if wait_count >= max_wait:
                print("⚠️ Timeout waiting for AJAX results, proceeding anyway...")

            # Additional wait for DOM to update
            time.sleep(3)

            # Debug: Check what's on the page
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
            if "No records found" in page_text or "No data available" in page_text:
                print("📋 Search completed but no records found for the selected date range")
                print("💡 Try a different date range - there might be no court orders for this date")
                return []

            if "TOTAL BENCH" in page_text:
                print("✓ Results page loaded successfully")
                # Extract total count if available
                if "Total Number of Cases" in page_text:
                    import re
                    match = re.search(r'Total Number of Cases\s*:\s*(\d+)', page_text)
                    if match:
                        total_cases = match.group(1)
                        print(f"📊 Total cases found: {total_cases}")

            # Find all PDF links in the orders column
            pdf_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='display_pdf.php']")

            # Also try alternative selectors
            if not pdf_links:
                print("🔍 Trying alternative selectors for PDF links...")
                alternative_selectors = [
                    "a[href*='pdf']",
                    "a[target='_blank']",
                    "a[id='orderid']",
                    "a[href*='cases/display_pdf.php']"
                ]

                for selector in alternative_selectors:
                    pdf_links = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if pdf_links:
                        print(f"✓ Found {len(pdf_links)} links using selector: {selector}")
                        break

            if not pdf_links:
                print("✗ No PDF links found with any selector")
                print("🔍 Checking page content for debugging...")

                # Save page source for debugging
                with open("debug_page.html", "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                print("💾 Page source saved to debug_page.html for inspection")

                return []

            links_data = []
            for i, link in enumerate(pdf_links, 1):
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()

                    # Skip if not a PDF link
                    if not href or 'display_pdf.php' not in href:
                        continue

                    # Try to get case number from different possible locations
                    case_number = f"case_{i}"
                    try:
                        # Try to find case number in the same row
                        row = link.find_element(By.XPATH, "./ancestor::tr")
                        case_cells = row.find_elements(By.TAG_NAME, "td")
                        if len(case_cells) > 1:
                            case_number = case_cells[1].text.strip()
                    except:
                        # If row method fails, try to extract from URL parameters
                        try:
                            import urllib.parse
                            parsed_url = urllib.parse.urlparse(href)
                            query_params = urllib.parse.parse_qs(parsed_url.query)
                            if 'caseno' in query_params:
                                case_number = query_params['caseno'][0]
                        except:
                            # Use link text or default
                            case_number = text.replace(' ', '_') if text else f"case_{i}"

                    # Clean case number for filename
                    safe_case_number = case_number.replace('/', '_').replace('\\', '_').replace(':', '_')
                    safe_text = text.replace(' ', '_').replace('/', '_') if text else 'Order'

                    links_data.append({
                        'url': href,
                        'type': text,
                        'case_number': case_number,
                        'filename': f"{safe_case_number}_{safe_text}.pdf"
                    })

                    print(f"✓ Found PDF: {case_number} - {text}")

                except Exception as e:
                    print(f"Warning: Error processing link {i}: {e}")
                    continue

            print(f"✓ Found {len(links_data)} PDF links")
            return links_data

        except Exception as e:
            print(f"✗ Error extracting PDF links: {e}")
            return []

    def download_pdf(self, pdf_data):
        """Download a single PDF file"""
        try:
            url = pdf_data['url']
            filename = pdf_data['filename']
            case_number = pdf_data['case_number']
            order_type = pdf_data['type']

            print(f"📥 Downloading: {filename}")

            # Get cookies from selenium session
            selenium_cookies = self.driver.get_cookies()
            for cookie in selenium_cookies:
                self.session.cookies.set(cookie['name'], cookie['value'])

            # Set headers to mimic browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': self.base_url
            }

            response = self.session.get(url, headers=headers, stream=True)
            response.raise_for_status()

            # Save the PDF
            file_path = self.download_dir / filename
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            print(f"✓ Downloaded: {filename} ({response.headers.get('content-length', 'Unknown')} bytes)")
            return True

        except Exception as e:
            print(f"✗ Error downloading {filename}: {e}")
            return False

    def download_all_pdfs(self, pdf_links):
        """Download all PDF files"""
        print(f"\n📥 Starting download of {len(pdf_links)} PDF files...")

        successful_downloads = 0
        failed_downloads = 0

        for i, pdf_data in enumerate(pdf_links, 1):
            print(f"\n[{i}/{len(pdf_links)}] Processing: {pdf_data['case_number']} - {pdf_data['type']}")

            if self.download_pdf(pdf_data):
                successful_downloads += 1
            else:
                failed_downloads += 1

            # Small delay between downloads
            time.sleep(1)

        print(f"\n📊 Download Summary:")
        print(f"✓ Successful: {successful_downloads}")
        print(f"✗ Failed: {failed_downloads}")
        print(f"📁 Files saved to: {self.download_dir.absolute()}")

        return successful_downloads, failed_downloads

    def run_scraper(self, from_date=None, to_date=None):
        """Main method to run the complete scraping process"""
        print("🚀 Starting Court Order Scraper for Madras High Court")
        print("=" * 60)
        print("📝 Note: You will need to manually enter the captcha when prompted")
        print(f"⏰ The script will wait up to {self.captcha_wait_minutes} minutes for captcha input")
        print("🌐 A Chrome browser window will open - don't close it!")
        print()

        try:
            # Setup driver
            if not self.setup_driver():
                return False

            # Navigate to the website
            print(f"🌐 Navigating to: {self.base_url}")
            self.driver.get(self.base_url)

            # Wait for page to fully load
            print("⏳ Waiting for page to load completely...")
            if not self.wait_for_page_load():
                print("❌ Page failed to load properly")
                return False

            # Handle any initial popups or modals
            self.handle_modal_dialogs()
            time.sleep(2)

            # Execute all steps
            steps = [
                self.click_court_orders_menu,
                self.select_court_and_bench,
                self.click_order_by_date,
                lambda: self.set_date_range(from_date, to_date),
                lambda: self.handle_captcha(self.captcha_wait_minutes),
                self.click_go_button
            ]

            for step in steps:
                if not step():
                    print("❌ Scraping failed at one of the steps")
                    return False
                time.sleep(1)

            # Extract and download PDFs
            pdf_links = self.extract_pdf_links()
            if pdf_links:
                self.download_all_pdfs(pdf_links)
                print("\n🎉 Scraping completed successfully!")
                return True
            else:
                print("❌ No PDF links found to download")
                return False

        except Exception as e:
            print(f"❌ Unexpected error during scraping: {e}")
            return False
        finally:
            if self.driver:
                print("\n🔄 Closing browser...")
                self.driver.quit()

    def __del__(self):
        """Cleanup method"""
        if hasattr(self, 'driver') and self.driver:
            self.driver.quit()


def main():
    """Main function with command line interface"""
    import argparse

    parser = argparse.ArgumentParser(description='Download court orders from Madras High Court')
    parser.add_argument('--from-date', type=str, help='From date (DD-MM-YYYY format)', default=None)
    parser.add_argument('--to-date', type=str, help='To date (DD-MM-YYYY format)', default=None)
    parser.add_argument('--download-dir', type=str, help='Download directory', default='downloads')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    parser.add_argument('--captcha-wait', type=int, help='Minutes to wait for captcha input (default: 5)', default=5)

    args = parser.parse_args()

    # Validate date format if provided
    if args.from_date:
        try:
            datetime.strptime(args.from_date, '%d-%m-%Y')
        except ValueError:
            print("❌ Invalid from-date format. Use DD-MM-YYYY")
            return

    if args.to_date:
        try:
            datetime.strptime(args.to_date, '%d-%m-%Y')
        except ValueError:
            print("❌ Invalid to-date format. Use DD-MM-YYYY")
            return

    # Create and run scraper
    scraper = CourtOrderScraper(
        download_dir=args.download_dir,
        headless=args.headless,
        captcha_wait_minutes=args.captcha_wait
    )

    print("📋 Configuration:")
    print(f"   From Date: {args.from_date or 'Yesterday'}")
    print(f"   To Date: {args.to_date or 'Today'}")
    print(f"   Download Directory: {args.download_dir}")
    print(f"   Headless Mode: {args.headless}")
    print(f"   Captcha Wait Time: {args.captcha_wait} minutes")
    print()

    success = scraper.run_scraper(from_date=args.from_date, to_date=args.to_date)

    if success:
        print("\n✅ Script completed successfully!")
    else:
        print("\n❌ Script failed. Please check the error messages above.")


if __name__ == "__main__":
    main()