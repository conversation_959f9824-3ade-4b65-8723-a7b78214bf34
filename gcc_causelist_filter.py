#!/usr/bin/env python3
"""
GCC Cause List Filter
Filters GCC (Greater Chennai Corporation) related cases from cause list PDFs
and outputs them in structured JSON and PDF formats.
"""

import os
import re
import json
import csv
from datetime import datetime
from pathlib import Path

try:
    import PyPDF2
except ImportError:
    print("❌ PyPDF2 not found. Install with: pip install PyPDF2")
    exit(1)

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    print("⚠️ ReportLab not found. PDF generation will be skipped. Install with: pip install reportlab")
    REPORTLAB_AVAILABLE = False

try:
    from openai import OpenAI
    LLM_AVAILABLE = True
except ImportError:
    print("⚠️ OpenAI library not found. LLM filtering will be skipped. Install with: pip install openai")
    LLM_AVAILABLE = False

class GCCCauseListFilter:
    def __init__(self, input_dir="Cause list", output_dir="GCC_Filtered_Cases", use_llm=True):
        """
        Initialize the GCC cause list filter

        Args:
            input_dir (str): Directory containing cause list PDFs
            output_dir (str): Directory to save filtered results
            use_llm (bool): Whether to use LLM for intelligent filtering
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.use_llm = use_llm and LLM_AVAILABLE

        # Setup LLM client if available
        if self.use_llm:
            self.setup_llm_client()

        # Load keyword files
        self.abbreviations = self.load_abbreviations()
        self.keywords = self.load_keywords()

        # Compile regex patterns
        self.gcc_patterns = self.compile_gcc_patterns()

        # Results storage
        self.filtered_cases = []

    def setup_llm_client(self):
        """Setup the NVIDIA LLM client"""
        try:
            self.llm_client = OpenAI(
                base_url="https://integrate.api.nvidia.com/v1",
                api_key="**********************************************************************"
            )
            print("✓ LLM client initialized successfully")
        except Exception as e:
            print(f"⚠️ Failed to initialize LLM client: {e}")
            self.use_llm = False

    def analyze_case_with_llm(self, case_text):
        """Use LLM to analyze if a case is GCC-related and extract structured information"""
        if not self.use_llm:
            return None

        try:
            prompt = f"""
You are an expert legal document analyzer specializing in Indian court cases. Analyze the following court case text and determine if it involves the Greater Chennai Corporation (GCC) or any Chennai municipal/local authority matters.

Case Text:
{case_text}

Please analyze and respond with a JSON object containing:
1. "is_gcc_related": true/false - whether this case involves GCC or Chennai municipal matters
2. "confidence": 0.0-1.0 - confidence level in the assessment
3. "gcc_indicators": list of specific phrases/terms that indicate GCC involvement
4. "case_details": object with extracted information:
   - "case_number": extracted case number (e.g., "WP/31730/2025")
   - "case_type": type of case (e.g., "WP", "WMP", "CRP")
   - "year": case year
   - "petitioner": petitioner name
   - "respondent": respondent name
   - "advocates": list of advocate names
   - "purpose": purpose/stage of the case
   - "zone_info": any zone/ward information mentioned
   - "location": location information (usually Chennai)

GCC-related indicators include:
- Greater Chennai Corporation, Chennai Corporation, GCC
- Commissioner (when related to Chennai)
- Executive Engineer, Assistant Engineer (Chennai context)
- Zonal Officer, Assistant Commissioner (Chennai)
- Municipal matters: building permits, demolition, encroachment, property tax
- Chennai municipal services: water supply, sanitation, waste management, roads
- Chennai wards, zones, municipal boundaries

Respond only with valid JSON, no additional text.
"""

            completion = self.llm_client.chat.completions.create(
                model="nvidia/llama-3.1-nemotron-ultra-253b-v1",
                messages=[
                    {"role": "system", "content": "You are a legal document analyzer. Respond only with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                top_p=0.9,
                max_tokens=2048,
                frequency_penalty=0,
                presence_penalty=0
            )

            response_text = completion.choices[0].message.content.strip()

            # Parse JSON response
            try:
                analysis = json.loads(response_text)
                return analysis
            except json.JSONDecodeError:
                # Try to extract JSON from response if it's wrapped in other text
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                    return analysis
                else:
                    print(f"⚠️ Failed to parse LLM response as JSON: {response_text[:200]}...")
                    return None

        except Exception as e:
            print(f"⚠️ LLM analysis failed: {e}")
            return None

    def load_abbreviations(self):
        """Load case type abbreviations from CSV"""
        abbreviations = {}
        csv_path = "madras_cause_list_abbreviations.csv"
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    abbreviations[row['Abbreviation']] = row['Full Form']
            print(f"✓ Loaded {len(abbreviations)} abbreviations from {csv_path}")
        except Exception as e:
            print(f"⚠️ Error loading abbreviations: {e}")
            
        return abbreviations

    def load_keywords(self):
        """Load GCC-related keywords from CSV"""
        keywords = {
            'entities': [],
            'case_types': [],
            'purposes': [],
            'subjects': []
        }
        csv_path = "chennai_cause_list_keywords.csv"
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if row['Entity']:
                        keywords['entities'].append(row['Entity'])
                    if row['Case Types']:
                        keywords['case_types'].append(row['Case Types'])
                    if row['Purpose/Stage']:
                        keywords['purposes'].append(row['Purpose/Stage'])
                    if row['Subjects']:
                        keywords['subjects'].append(row['Subjects'])
            print(f"✓ Loaded keywords from {csv_path}")
        except Exception as e:
            print(f"⚠️ Error loading keywords: {e}")
            
        return keywords

    def compile_gcc_patterns(self):
        """Compile regex patterns for GCC identification using comprehensive patterns from gcc_filter_2.py"""

        # Comprehensive GCC terms from gcc_filter_2.py
        gcc_terms = [
            # Core GCC identifiers
            r"Greater\s+Chennai\s+Corporation",
            r"\bGCC\b",  # More precise word boundary
            r"Corporation\s+of\s+Chennai",
            r"Chennai\s+City\s+Municipal\s+Corporation",
            r"Chennai\s+Municipal\s+Corporation",
            r"Municipal\s+Corporation\s+Act",
            r"Tamil\s+Nadu\s+Town\s+and\s+Country\s+Planning\s+Act",
            r"Chennai\s+City\s+Municipal\s+Corporation\s+Act",

            # GCC officials and departments
            r"Commissioner.*Chennai\s+Corporation",
            r"Chennai\s+Corporation.*Commissioner",
            r"Mayor.*Chennai",
            r"Deputy\s+Mayor.*Chennai",
            r"Zonal\s+Officer.*Chennai",
            r"Assistant\s+Commissioner.*Chennai",
            r"Assistant\s+Engineer.*GCC",
            r"Executive\s+Engineer.*GCC",
            r"Executive\s+Engineer.*Chennai",

            # GCC-specific locations and addresses
            r"Ripon\s+Building",  # GCC headquarters
            r"Corporation\s+Office.*Chennai",
            r"Chennai\s+Corporation\s+Office",

            # 1. URBAN PLANNING (GCC responsibility)
            r"(?:Urban\s+Planning|City\s+Planning|Master\s+Plan|Development\s+Plan).*Chennai",
            r"Chennai.*(?:Urban\s+Planning|City\s+Planning|Master\s+Plan|Development\s+Plan)",
            r"(?:Zoning|Land\s+Use|Layout\s+Approval).*Chennai",
            r"Chennai.*(?:Zoning|Land\s+Use|Layout\s+Approval)",

            # 2. ROAD MAINTENANCE (GCC responsibility)
            r"(?:Road\s+Maintenance|Road\s+Repair|Street\s+Repair|Pothole).*Chennai",
            r"Chennai.*(?:Road\s+Maintenance|Road\s+Repair|Street\s+Repair|Pothole)",
            r"(?:Footpath|Pavement|Sidewalk).*Chennai",
            r"Chennai.*(?:Footpath|Pavement|Sidewalk)",

            # 3. WATER SUPPLY (GCC responsibility)
            r"(?:Water\s+Supply|Water\s+Connection|Water\s+Board|Metro\s+Water).*Chennai",
            r"Chennai.*(?:Water\s+Supply|Water\s+Connection|Water\s+Board|Metro\s+Water)",
            r"(?:Drinking\s+Water|Potable\s+Water|Water\s+Quality).*Chennai",
            r"Chennai.*(?:Drinking\s+Water|Potable\s+Water|Water\s+Quality)",

            # 4. SANITATION (GCC responsibility)
            r"(?:Sanitation|Sewage|Drainage|Storm\s+Water).*Chennai",
            r"Chennai.*(?:Sanitation|Sewage|Drainage|Storm\s+Water)",
            r"(?:Toilet|Public\s+Convenience|Latrine).*Chennai",
            r"Chennai.*(?:Toilet|Public\s+Convenience|Latrine)",

            # 5. WASTE MANAGEMENT (GCC responsibility)
            r"(?:Waste\s+Management|Garbage|Solid\s+Waste|Refuse).*Chennai",
            r"Chennai.*(?:Waste\s+Management|Garbage|Solid\s+Waste|Refuse)",
            r"(?:Waste\s+Collection|Waste\s+Disposal|Landfill|Dumping).*Chennai",
            r"Chennai.*(?:Waste\s+Collection|Waste\s+Disposal|Landfill|Dumping)",

            # 6. PUBLIC HEALTH (GCC responsibility)
            r"(?:Public\s+Health|Health\s+Department|Medical\s+Officer).*Chennai",
            r"Chennai.*(?:Public\s+Health|Health\s+Department|Medical\s+Officer)",
            r"(?:Epidemic|Disease\s+Control|Vector\s+Control|Mosquito).*Chennai",
            r"Chennai.*(?:Epidemic|Disease\s+Control|Vector\s+Control|Mosquito)",

            # 7. STREET LIGHTING (GCC responsibility)
            r"(?:Street\s+Light|Street\s+Lamp|Public\s+Lighting).*Chennai",
            r"Chennai.*(?:Street\s+Light|Street\s+Lamp|Public\s+Lighting)",
            r"(?:LED\s+Light|Sodium\s+Light|Electric\s+Post).*Chennai",
            r"Chennai.*(?:LED\s+Light|Sodium\s+Light|Electric\s+Post)",

            # 8. PARKS AND RECREATION (GCC responsibility)
            r"(?:Public\s+Park|Municipal\s+Park|Corporation\s+Park|Playground|Recreation\s+Center).*Chennai",
            r"Chennai.*(?:Public\s+Park|Municipal\s+Park|Corporation\s+Park|Playground|Recreation\s+Center)",
            r"(?:Green\s+Space|Open\s+Space|Public\s+Garden|Children\s+Park).*Chennai\s+Corporation",
            r"Chennai\s+Corporation.*(?:Green\s+Space|Open\s+Space|Public\s+Garden|Children\s+Park)",
            r"(?:Park\s+Maintenance|Garden\s+Maintenance).*Chennai",
            r"Chennai.*(?:Park\s+Maintenance|Garden\s+Maintenance)",

            # 9. BUILDING REGULATION (GCC responsibility)
            r"(?:Building\s+Permit|Building\s+Approval|Construction\s+Permit).*Chennai",
            r"Chennai.*(?:Building\s+Permit|Building\s+Approval|Construction\s+Permit)",
            r"(?:Building\s+Plan|Occupancy\s+Certificate|Completion\s+Certificate).*Chennai",
            r"Chennai.*(?:Building\s+Plan|Occupancy\s+Certificate|Completion\s+Certificate)",
            r"(?:Unauthorized\s+Construction|Illegal\s+Construction|Demolition).*Chennai",
            r"Chennai.*(?:Unauthorized\s+Construction|Illegal\s+Construction|Demolition)",

            # 10. TAXATION AND REVENUE (GCC responsibility)
            r"(?:Property\s+Tax|House\s+Tax|Assessment|Revenue).*Chennai",
            r"Chennai.*(?:Property\s+Tax|House\s+Tax|Assessment|Revenue)",
            r"(?:Trade\s+License|Professional\s+Tax|Advertisement\s+Tax).*Chennai",
            r"Chennai.*(?:Trade\s+License|Professional\s+Tax|Advertisement\s+Tax)",

            # 11. MARKET AND COMMERCIAL (GCC responsibility)
            r"(?:Market|Commercial\s+Complex|Shopping\s+Complex).*Chennai",
            r"Chennai.*(?:Market|Commercial\s+Complex|Shopping\s+Complex)",
            r"(?:Vendor|Hawker|Street\s+Vendor).*Chennai",
            r"Chennai.*(?:Vendor|Hawker|Street\s+Vendor)",

            # 12. WARD AND ADMINISTRATIVE (GCC responsibility)
            r"Ward.*Chennai",
            r"Chennai.*Ward",
            r"(?:Zone|Division|Circle).*Chennai\s+Corporation",
            r"Chennai\s+Corporation.*(?:Zone|Division|Circle)",
            r"(?:Councillor|Corporator).*Chennai",
            r"Chennai.*(?:Councillor|Corporator)",

            # 13. LEGAL ACTS AND REGULATIONS
            r"Chennai\s+City\s+Municipal\s+Corporation\s+Act",
            r"Tamil\s+Nadu\s+District\s+Municipalities\s+Act.*Chennai",
            r"(?:Municipal|Corporation).*(?:Act|Rules|Regulations).*Chennai",
            r"Chennai.*(?:Municipal|Corporation).*(?:Act|Rules|Regulations)",

            # 14. GENERAL MUNICIPAL SERVICES IN CHENNAI
            r"(?:Municipal\s+Services|Civic\s+Services|Public\s+Services).*Chennai",
            r"Chennai.*(?:Municipal\s+Services|Civic\s+Services|Public\s+Services)",
            r"(?:Birth\s+Certificate|Death\s+Certificate|Marriage\s+Certificate).*Chennai",
            r"Chennai.*(?:Birth\s+Certificate|Death\s+Certificate|Marriage\s+Certificate)",
        ]

        # Add patterns from keyword files
        for entity in self.keywords['entities']:
            if entity.strip():
                gcc_terms.append(re.escape(entity))

        for subject in self.keywords['subjects']:
            if subject.strip():
                gcc_terms.append(re.escape(subject))

        # Compile combined regex
        combined_pattern = re.compile("|".join(f"({p})" for p in gcc_terms), re.IGNORECASE)

        print(f"✓ Compiled {len(gcc_terms)} GCC identification patterns")
        return combined_pattern

    def extract_case_info(self, text_line):
        """Extract case information from a text line"""
        case_info = {
            'case_number': '',
            'case_type': '',
            'year': '',
            'petitioner': '',
            'respondent': '',
            'advocate': '',
            'purpose': '',
            'zone_info': '',
            'court_hall': '',
            'bench': '',
            'location': '',
            'raw_text': text_line.strip()
        }

        # Extract case number pattern (e.g., WMP 33330/2025, WP/31730/2025, WP 31730/2025)
        case_patterns = [
            r'(WMP?)\s*(\d+/\d{4})',           # WMP or WM followed by number/year
            r'(WP)\s*(\d+/\d{4})',             # WP followed by number/year
            r'(CRP?)\s*(\d+/\d{4})',           # CRP or CR followed by number/year
            r'(CMA?)\s*(\d+/\d{4})',           # CMA or CM followed by number/year
            r'([A-Z]+\.?[A-Z]*)\s*(\d+/\d{4})', # General pattern for any case type
        ]

        for pattern in case_patterns:
            case_match = re.search(pattern, text_line)
            if case_match:
                case_info['case_type'] = case_match.group(1)
                case_info['case_number'] = case_match.group(2)
                case_info['year'] = case_match.group(2).split('/')[-1]
                break

        # Extract VS pattern for petitioner vs respondent (more flexible)
        vs_patterns = [
            r'(\d+/\d{4})\s+(.+?)\s+VS\s*(.+?)(?:\s+[A-Z][A-Z\s\.]+\s*-{3,}|$)',  # After case number
            r'(.+?)\s+VS\s*(.+?)(?:\s+[A-Z][A-Z\s\.]+\s*-{3,}|$)',                # General VS pattern
        ]

        for pattern in vs_patterns:
            vs_match = re.search(pattern, text_line, re.IGNORECASE)
            if vs_match:
                if len(vs_match.groups()) == 3:  # Pattern with case number
                    case_info['petitioner'] = vs_match.group(2).strip()
                    case_info['respondent'] = vs_match.group(3).strip()
                else:  # General pattern
                    case_info['petitioner'] = vs_match.group(1).strip()
                    case_info['respondent'] = vs_match.group(2).strip()
                break

        # Extract advocate names (improved patterns)
        advocate_patterns = [
            r'([A-Z][A-Z\s\.]+)\s*-{3,}',      # Names followed by dashes
            r'M/S\.?\s*([A-Z][A-Z\s\.]+)',      # M/S. prefix
            r'([A-Z][A-Z\s\.]{15,})',           # Long uppercase names (likely advocates)
        ]

        for pattern in advocate_patterns:
            advocate_match = re.search(pattern, text_line)
            if advocate_match:
                advocate_name = advocate_match.group(1).strip()
                # Clean up advocate name
                advocate_name = re.sub(r'\s+', ' ', advocate_name)  # Remove extra spaces
                advocate_name = advocate_name.rstrip('.-')  # Remove trailing dots and dashes
                case_info['advocate'] = advocate_name
                break

        # Extract purpose/stage information (enhanced)
        purpose_patterns = [
            r'FOR\s+(ADMISSION|ORDERS|FINAL\s+DISPOSAL|HEARING|DIRECTIONS)',
            r'(Stay|To\s+Dispense\s+With|Interim\s+directions?|Dispensed\s+with)',
            r'\((WP|PIL|Local\s+Auth\.|Ultra\s+Vires)\)',
        ]

        for pattern in purpose_patterns:
            purpose_match = re.search(pattern, text_line, re.IGNORECASE)
            if purpose_match:
                case_info['purpose'] = purpose_match.group(1) if purpose_match.group(1) else purpose_match.group(0)
                break

        # Look for zone information
        zone_patterns = [
            r'Zone\s*(\d+)',
            r'ZONE\s*(\d+)',
            r'(\d+)\s*ZONE',
        ]

        for pattern in zone_patterns:
            zone_match = re.search(pattern, text_line, re.IGNORECASE)
            if zone_match:
                case_info['zone_info'] = f"Zone {zone_match.group(1)}"
                break

        # Extract court hall information
        court_hall_match = re.search(r'(\d+/\d+)\s+(CJ|J|SMJ)', text_line)
        if court_hall_match:
            case_info['court_hall'] = court_hall_match.group(1)
            case_info['bench'] = court_hall_match.group(2)

        # Extract location information (CHENNAI)
        if 'CHENNAI' in text_line.upper():
            case_info['location'] = 'CHENNAI'

        return case_info

    def process_pdf(self, pdf_path):
        """Process a single PDF file and extract GCC-related cases"""
        print(f"📄 Processing: {pdf_path.name}")

        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                full_text = ""

                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        full_text += text + "\n"

                # Debug: Show first 500 characters of extracted text
                print(f"  📝 Extracted text preview: {full_text[:500]}...")
                print(f"  📊 Total text length: {len(full_text)} characters")

                # Process text in chunks to handle multi-line cases
                gcc_cases = []

                # Extract case blocks using improved pattern
                case_blocks = self.extract_case_blocks(full_text)
                print(f"  🧩 Case blocks found: {len(case_blocks)}")

                gcc_matches = 0
                for i, block in enumerate(case_blocks):
                    if not block.strip():
                        continue

                    # First check with regex patterns
                    regex_match = self.gcc_patterns.search(block)

                    # If regex matches or if we want to use LLM for all blocks
                    if regex_match or self.use_llm:
                        print(f"  🔍 Analyzing block {i+1}/{len(case_blocks)}")

                        # Use LLM for intelligent analysis
                        if self.use_llm:
                            llm_analysis = self.analyze_case_with_llm(block)

                            if llm_analysis and llm_analysis.get('is_gcc_related', False):
                                gcc_matches += 1
                                print(f"  🎯 LLM identified GCC case (confidence: {llm_analysis.get('confidence', 0):.2f})")
                                print(f"    Indicators: {llm_analysis.get('gcc_indicators', [])}")

                                # Use LLM-extracted case details if available
                                if llm_analysis.get('case_details'):
                                    case_info = llm_analysis['case_details']
                                    case_info['llm_confidence'] = llm_analysis.get('confidence', 0)
                                    case_info['gcc_indicators'] = llm_analysis.get('gcc_indicators', [])
                                else:
                                    case_info = self.extract_case_info(block)
                                    case_info['llm_confidence'] = llm_analysis.get('confidence', 0)
                                    case_info['gcc_indicators'] = llm_analysis.get('gcc_indicators', [])

                                case_info['source_file'] = pdf_path.name
                                case_info['date_processed'] = datetime.now().isoformat()
                                case_info['raw_text'] = block[:500] + '...' if len(block) > 500 else block
                                case_info['analysis_method'] = 'LLM'

                                gcc_cases.append(case_info)
                                print(f"    ✓ Added case: {case_info.get('case_type', 'Unknown')} {case_info.get('case_number', 'Unknown')}")

                        # Fallback to regex-only analysis
                        elif regex_match:
                            gcc_matches += 1
                            print(f"  🎯 Regex pattern match found")

                            case_info = self.extract_case_info(block)
                            case_info['source_file'] = pdf_path.name
                            case_info['date_processed'] = datetime.now().isoformat()
                            case_info['raw_text'] = block[:500] + '...' if len(block) > 500 else block
                            case_info['analysis_method'] = 'Regex'

                            gcc_cases.append(case_info)
                            print(f"    ✓ Added case: {case_info['case_type']} {case_info['case_number']}")

                print(f"  📈 Total GCC cases found: {gcc_matches}")
                return gcc_cases

    def extract_case_blocks(self, text):
        """Extract individual case blocks from the cause list text"""
        # Split by case number patterns or common separators
        patterns = [
            r'\n(?=\s*(?:AND\s+)?(?:WMP?|WP|CRP?|CMA?|AS|SA|OA|HCP|CONT\s*P)\s+\d+)',  # Case number patterns
            r'\n(?=\s*\d+\s+(?:WMP?|WP|CRP?|CMA?|AS|SA|OA|HCP|CONT\s*P))',  # Numbered cases
            r'\n\s*-{4,}\s*\n',  # Dash separators
            r'\n\s*={4,}\s*\n',  # Equal sign separators
        ]

        # Try each pattern and use the one that gives the most blocks
        best_blocks = [text]

        for pattern in patterns:
            try:
                blocks = re.split(pattern, text)
                if len(blocks) > len(best_blocks):
                    best_blocks = blocks
            except Exception as e:
                continue

        # Clean and filter blocks
        cleaned_blocks = []
        for block in best_blocks:
            block = block.strip()
            if len(block) > 50 and re.search(r'[A-Z]+\s*\d+/\d{4}', block):  # Must contain case number
                cleaned_blocks.append(block)

        return cleaned_blocks

        except Exception as e:
            print(f"  ✗ Error processing {pdf_path.name}: {e}")
            return []

    def process_all_pdfs(self):
        """Process all PDF files in the input directory"""
        print(f"🔍 Scanning for PDFs in: {self.input_dir}")
        
        pdf_files = list(self.input_dir.glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found in the input directory")
            return
        
        print(f"📚 Found {len(pdf_files)} PDF files to process")
        
        for pdf_file in pdf_files:
            cases = self.process_pdf(pdf_file)
            self.filtered_cases.extend(cases)
        
        print(f"\n📊 Summary:")
        print(f"   Total PDFs processed: {len(pdf_files)}")
        print(f"   Total GCC cases found: {len(self.filtered_cases)}")

    def save_to_json(self):
        """Save filtered cases to JSON file"""
        json_path = self.output_dir / f"gcc_cases_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output_data = {
            'metadata': {
                'total_cases': len(self.filtered_cases),
                'generated_on': datetime.now().isoformat(),
                'filter_criteria': 'GCC (Greater Chennai Corporation) related cases'
            },
            'cases': self.filtered_cases
        }
        
        with open(json_path, 'w', encoding='utf-8') as file:
            json.dump(output_data, file, indent=2, ensure_ascii=False)
        
        print(f"💾 JSON saved to: {json_path}")
        return json_path

    def create_pdf_report(self):
        """Create a structured PDF report of filtered cases"""
        if not REPORTLAB_AVAILABLE:
            print("⚠️ Skipping PDF report generation (ReportLab not available)")
            return None

        pdf_path = self.output_dir / f"gcc_cases_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        doc = SimpleDocTemplate(str(pdf_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        story.append(Paragraph("GCC Related Cases - Cause List Analysis", title_style))
        story.append(Spacer(1, 20))
        
        # Summary
        summary_data = [
            ['Total Cases Found:', str(len(self.filtered_cases))],
            ['Report Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Filter Criteria:', 'Greater Chennai Corporation (GCC) related cases']
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 3*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 30))
        
        # Cases details
        if self.filtered_cases:
            story.append(Paragraph("Case Details", styles['Heading2']))
            story.append(Spacer(1, 12))
            
            for i, case in enumerate(self.filtered_cases, 1):
                case_data = [
                    ['Case No.:', f"{case['case_type']} {case['case_number']}"],
                    ['Year:', case['year']],
                    ['Petitioner:', case['petitioner'][:50] + '...' if len(case['petitioner']) > 50 else case['petitioner']],
                    ['Respondent:', case['respondent'][:50] + '...' if len(case['respondent']) > 50 else case['respondent']],
                    ['Advocate:', case['advocate'][:50] + '...' if len(case['advocate']) > 50 else case['advocate']],
                    ['Purpose:', case['purpose']],
                    ['Zone Info:', case['zone_info']],
                    ['Source File:', case['source_file']]
                ]
                
                case_table = Table(case_data, colWidths=[1.5*inch, 4*inch])
                case_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(Paragraph(f"Case {i}", styles['Heading3']))
                story.append(case_table)
                story.append(Spacer(1, 15))
        
        doc.build(story)
        print(f"📄 PDF report saved to: {pdf_path}")
        return pdf_path

    def run_filter(self):
        """Main method to run the filtering process"""
        print("🚀 Starting GCC Cause List Filter")
        print("=" * 60)
        print(f"📁 Input Directory: {self.input_dir.absolute()}")
        print(f"📁 Output Directory: {self.output_dir.absolute()}")
        print()
        
        # Process all PDFs
        self.process_all_pdfs()
        
        if not self.filtered_cases:
            print("❌ No GCC-related cases found in the cause lists")
            return False
        
        # Save results
        json_path = self.save_to_json()
        pdf_path = self.create_pdf_report()

        print(f"\n🎉 Filtering completed successfully!")
        print(f"📊 Found {len(self.filtered_cases)} GCC-related cases")
        print(f"💾 Results saved to:")
        print(f"   JSON: {json_path}")
        if pdf_path:
            print(f"   PDF:  {pdf_path}")

        return True


def main():
    """Main function with command line interface"""
    import argparse

    parser = argparse.ArgumentParser(description='Filter GCC cases from cause list PDFs')
    parser.add_argument('--input-dir', type=str, help='Input directory containing PDFs', default='Cause list')
    parser.add_argument('--output-dir', type=str, help='Output directory for results', default='GCC_Filtered_Cases')
    parser.add_argument('--use-llm', action='store_true', help='Use LLM for intelligent filtering (default: True)', default=True)
    parser.add_argument('--no-llm', action='store_true', help='Disable LLM and use only regex patterns')

    args = parser.parse_args()

    # Handle LLM option
    use_llm = args.use_llm and not args.no_llm

    # Create and run filter
    filter_tool = GCCCauseListFilter(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        use_llm=use_llm
    )

    print("📋 Configuration:")
    print(f"   Input Directory: {args.input_dir}")
    print(f"   Output Directory: {args.output_dir}")
    print(f"   LLM Analysis: {'Enabled' if use_llm else 'Disabled'}")
    print()

    success = filter_tool.run_filter()

    if success:
        print("\n✅ Script completed successfully!")
    else:
        print("\n❌ Script failed or no cases found.")


if __name__ == "__main__":
    main()
