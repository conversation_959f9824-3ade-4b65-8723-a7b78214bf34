#!/usr/bin/env python3
"""
GCC Cause List Filter
Filters GCC (Greater Chennai Corporation) related cases from cause list PDFs
and outputs them in structured JSON and PDF formats.
"""

import os
import re
import json
import csv
from datetime import datetime
from pathlib import Path

try:
    import PyPDF2
except ImportError:
    print("❌ PyPDF2 not found. Install with: pip install PyPDF2")
    exit(1)

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    print("⚠️ ReportLab not found. PDF generation will be skipped. Install with: pip install reportlab")
    REPORTLAB_AVAILABLE = False

class GCCCauseListFilter:
    def __init__(self, input_dir="Cause list", output_dir="GCC_Filtered_Cases"):
        """
        Initialize the GCC cause list filter
        
        Args:
            input_dir (str): Directory containing cause list PDFs
            output_dir (str): Directory to save filtered results
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Load keyword files
        self.abbreviations = self.load_abbreviations()
        self.keywords = self.load_keywords()
        
        # Compile regex patterns
        self.gcc_patterns = self.compile_gcc_patterns()
        
        # Results storage
        self.filtered_cases = []

    def load_abbreviations(self):
        """Load case type abbreviations from CSV"""
        abbreviations = {}
        csv_path = "madras_cause_list_abbreviations.csv"
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    abbreviations[row['Abbreviation']] = row['Full Form']
            print(f"✓ Loaded {len(abbreviations)} abbreviations from {csv_path}")
        except Exception as e:
            print(f"⚠️ Error loading abbreviations: {e}")
            
        return abbreviations

    def load_keywords(self):
        """Load GCC-related keywords from CSV"""
        keywords = {
            'entities': [],
            'case_types': [],
            'purposes': [],
            'subjects': []
        }
        csv_path = "chennai_cause_list_keywords.csv"
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if row['Entity']:
                        keywords['entities'].append(row['Entity'])
                    if row['Case Types']:
                        keywords['case_types'].append(row['Case Types'])
                    if row['Purpose/Stage']:
                        keywords['purposes'].append(row['Purpose/Stage'])
                    if row['Subjects']:
                        keywords['subjects'].append(row['Subjects'])
            print(f"✓ Loaded keywords from {csv_path}")
        except Exception as e:
            print(f"⚠️ Error loading keywords: {e}")
            
        return keywords

    def compile_gcc_patterns(self):
        """Compile regex patterns for GCC identification"""
        patterns = []
        
        # Core GCC entities
        gcc_entities = [
            r"Greater\s+Chennai\s+Corporation",
            r"\bGCC\b",
            r"Corporation\s+of\s+Chennai",
            r"Chennai\s+City\s+Municipal\s+Corporation",
            r"Chennai\s+Municipal\s+Corporation",
            r"Commissioner.*Chennai\s+Corporation",
            r"Chennai\s+Corporation.*Commissioner",
            r"Zonal\s+Officer.*Chennai",
            r"Assistant\s+Engineer.*GCC",
            r"Executive\s+Engineer.*GCC",
            r"Executive\s+Engineer.*Chennai",
            r"Assistant\s+Commissioner.*Chennai"
        ]
        
        # GCC-related subjects and departments
        gcc_subjects = [
            r"Building\s+Permission",
            r"Planning\s+Permission", 
            r"Demolition",
            r"Sealing",
            r"Encroachment",
            r"Unauthorized\s+Construction",
            r"Storm\s+Water\s+Drain",
            r"Sewage",
            r"Sanitation",
            r"Public\s+Health",
            r"Trade\s+Licence",
            r"Property\s+Tax",
            r"Assessment",
            r"Road\s+Maintenance",
            r"Footpath",
            r"Water\s+Supply",
            r"Waste\s+Management",
            r"Street\s+Light"
        ]
        
        # Combine all patterns
        all_patterns = gcc_entities + gcc_subjects
        
        # Add patterns from keyword file
        for entity in self.keywords['entities']:
            if entity.strip():
                patterns.append(re.escape(entity))
        
        for subject in self.keywords['subjects']:
            if subject.strip():
                patterns.append(re.escape(subject))
        
        # Compile combined regex
        combined_pattern = re.compile("|".join(f"({p})" for p in all_patterns), re.IGNORECASE)
        
        return combined_pattern

    def extract_case_info(self, text_line):
        """Extract case information from a text line"""
        case_info = {
            'case_number': '',
            'case_type': '',
            'year': '',
            'petitioner': '',
            'respondent': '',
            'advocate': '',
            'purpose': '',
            'zone_info': '',
            'court_hall': '',
            'bench': '',
            'raw_text': text_line.strip()
        }

        # Extract case number pattern (e.g., WMP 33330/2025, WP/31730/2025, WP 31730/2025)
        case_patterns = [
            r'(WMP?)\s*(\d+/\d{4})',           # WMP or WM followed by number/year
            r'(WP)\s*(\d+/\d{4})',             # WP followed by number/year
            r'(CRP?)\s*(\d+/\d{4})',           # CRP or CR followed by number/year
            r'(CMA?)\s*(\d+/\d{4})',           # CMA or CM followed by number/year
            r'([A-Z]+\.?[A-Z]*)\s*(\d+/\d{4})', # General pattern for any case type
        ]

        for pattern in case_patterns:
            case_match = re.search(pattern, text_line)
            if case_match:
                case_info['case_type'] = case_match.group(1)
                case_info['case_number'] = case_match.group(2)
                case_info['year'] = case_match.group(2).split('/')[-1]
                break

        # Extract VS pattern for petitioner vs respondent (more flexible)
        vs_patterns = [
            r'(\d+/\d{4})\s+(.+?)\s+VS\s*(.+?)(?:\s+[A-Z][A-Z\s\.]+\s*-{3,}|$)',  # After case number
            r'(.+?)\s+VS\s*(.+?)(?:\s+[A-Z][A-Z\s\.]+\s*-{3,}|$)',                # General VS pattern
        ]

        for pattern in vs_patterns:
            vs_match = re.search(pattern, text_line, re.IGNORECASE)
            if vs_match:
                if len(vs_match.groups()) == 3:  # Pattern with case number
                    case_info['petitioner'] = vs_match.group(2).strip()
                    case_info['respondent'] = vs_match.group(3).strip()
                else:  # General pattern
                    case_info['petitioner'] = vs_match.group(1).strip()
                    case_info['respondent'] = vs_match.group(2).strip()
                break

        # Extract advocate names (improved patterns)
        advocate_patterns = [
            r'([A-Z][A-Z\s\.]+)\s*-{3,}',      # Names followed by dashes
            r'M/S\.?\s*([A-Z][A-Z\s\.]+)',      # M/S. prefix
            r'([A-Z][A-Z\s\.]{15,})',           # Long uppercase names (likely advocates)
        ]

        for pattern in advocate_patterns:
            advocate_match = re.search(pattern, text_line)
            if advocate_match:
                advocate_name = advocate_match.group(1).strip()
                # Clean up advocate name
                advocate_name = re.sub(r'\s+', ' ', advocate_name)  # Remove extra spaces
                advocate_name = advocate_name.rstrip('.-')  # Remove trailing dots and dashes
                case_info['advocate'] = advocate_name
                break

        # Extract purpose/stage information (enhanced)
        purpose_patterns = [
            r'FOR\s+(ADMISSION|ORDERS|FINAL\s+DISPOSAL|HEARING|DIRECTIONS)',
            r'(Stay|To\s+Dispense\s+With|Interim\s+directions?|Dispensed\s+with)',
            r'\((WP|PIL|Local\s+Auth\.|Ultra\s+Vires)\)',
        ]

        for pattern in purpose_patterns:
            purpose_match = re.search(pattern, text_line, re.IGNORECASE)
            if purpose_match:
                case_info['purpose'] = purpose_match.group(1) if purpose_match.group(1) else purpose_match.group(0)
                break

        # Look for zone information
        zone_patterns = [
            r'Zone\s*(\d+)',
            r'ZONE\s*(\d+)',
            r'(\d+)\s*ZONE',
        ]

        for pattern in zone_patterns:
            zone_match = re.search(pattern, text_line, re.IGNORECASE)
            if zone_match:
                case_info['zone_info'] = f"Zone {zone_match.group(1)}"
                break

        # Extract court hall information
        court_hall_match = re.search(r'(\d+/\d+)\s+(CJ|J|SMJ)', text_line)
        if court_hall_match:
            case_info['court_hall'] = court_hall_match.group(1)
            case_info['bench'] = court_hall_match.group(2)

        # Extract location information (CHENNAI)
        if 'CHENNAI' in text_line.upper():
            case_info['location'] = 'CHENNAI'

        return case_info

    def process_pdf(self, pdf_path):
        """Process a single PDF file and extract GCC-related cases"""
        print(f"📄 Processing: {pdf_path.name}")

        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                full_text = ""

                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        full_text += text + "\n"

                # Process text in chunks to handle multi-line cases
                gcc_cases = []

                # Split by common case separators
                case_blocks = re.split(r'\n(?=\s*(?:AND\s+)?(?:WMP?|WP|CRP?|CMA?|AS|SA)\s+\d+)', full_text)

                for block in case_blocks:
                    block = block.strip()
                    if not block:
                        continue

                    # Check if block contains GCC-related content
                    if self.gcc_patterns.search(block):
                        # Process each line in the block
                        lines = block.split('\n')
                        current_case = None

                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue

                            # Check if this line starts a new case
                            case_start_match = re.search(r'(WMP?|WP|CRP?|CMA?|AS|SA)\s+\d+/\d{4}', line)
                            if case_start_match:
                                # Save previous case if exists
                                if current_case and current_case['case_number']:
                                    current_case['source_file'] = pdf_path.name
                                    current_case['date_processed'] = datetime.now().isoformat()
                                    gcc_cases.append(current_case)
                                    print(f"  ✓ Found GCC case: {current_case['case_type']} {current_case['case_number']}")

                                # Start new case
                                current_case = self.extract_case_info(line)
                            elif current_case:
                                # Continue processing current case (multi-line)
                                additional_info = self.extract_case_info(line)

                                # Merge information
                                for key, value in additional_info.items():
                                    if value and not current_case[key]:
                                        current_case[key] = value

                                # Append to raw text
                                current_case['raw_text'] += ' ' + line

                        # Save the last case in the block
                        if current_case and current_case['case_number']:
                            current_case['source_file'] = pdf_path.name
                            current_case['date_processed'] = datetime.now().isoformat()
                            gcc_cases.append(current_case)
                            print(f"  ✓ Found GCC case: {current_case['case_type']} {current_case['case_number']}")

                return gcc_cases

        except Exception as e:
            print(f"  ✗ Error processing {pdf_path.name}: {e}")
            return []

    def process_all_pdfs(self):
        """Process all PDF files in the input directory"""
        print(f"🔍 Scanning for PDFs in: {self.input_dir}")
        
        pdf_files = list(self.input_dir.glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found in the input directory")
            return
        
        print(f"📚 Found {len(pdf_files)} PDF files to process")
        
        for pdf_file in pdf_files:
            cases = self.process_pdf(pdf_file)
            self.filtered_cases.extend(cases)
        
        print(f"\n📊 Summary:")
        print(f"   Total PDFs processed: {len(pdf_files)}")
        print(f"   Total GCC cases found: {len(self.filtered_cases)}")

    def save_to_json(self):
        """Save filtered cases to JSON file"""
        json_path = self.output_dir / f"gcc_cases_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output_data = {
            'metadata': {
                'total_cases': len(self.filtered_cases),
                'generated_on': datetime.now().isoformat(),
                'filter_criteria': 'GCC (Greater Chennai Corporation) related cases'
            },
            'cases': self.filtered_cases
        }
        
        with open(json_path, 'w', encoding='utf-8') as file:
            json.dump(output_data, file, indent=2, ensure_ascii=False)
        
        print(f"💾 JSON saved to: {json_path}")
        return json_path

    def create_pdf_report(self):
        """Create a structured PDF report of filtered cases"""
        if not REPORTLAB_AVAILABLE:
            print("⚠️ Skipping PDF report generation (ReportLab not available)")
            return None

        pdf_path = self.output_dir / f"gcc_cases_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        doc = SimpleDocTemplate(str(pdf_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        story.append(Paragraph("GCC Related Cases - Cause List Analysis", title_style))
        story.append(Spacer(1, 20))
        
        # Summary
        summary_data = [
            ['Total Cases Found:', str(len(self.filtered_cases))],
            ['Report Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['Filter Criteria:', 'Greater Chennai Corporation (GCC) related cases']
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 3*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 30))
        
        # Cases details
        if self.filtered_cases:
            story.append(Paragraph("Case Details", styles['Heading2']))
            story.append(Spacer(1, 12))
            
            for i, case in enumerate(self.filtered_cases, 1):
                case_data = [
                    ['Case No.:', f"{case['case_type']} {case['case_number']}"],
                    ['Year:', case['year']],
                    ['Petitioner:', case['petitioner'][:50] + '...' if len(case['petitioner']) > 50 else case['petitioner']],
                    ['Respondent:', case['respondent'][:50] + '...' if len(case['respondent']) > 50 else case['respondent']],
                    ['Advocate:', case['advocate'][:50] + '...' if len(case['advocate']) > 50 else case['advocate']],
                    ['Purpose:', case['purpose']],
                    ['Zone Info:', case['zone_info']],
                    ['Source File:', case['source_file']]
                ]
                
                case_table = Table(case_data, colWidths=[1.5*inch, 4*inch])
                case_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(Paragraph(f"Case {i}", styles['Heading3']))
                story.append(case_table)
                story.append(Spacer(1, 15))
        
        doc.build(story)
        print(f"📄 PDF report saved to: {pdf_path}")
        return pdf_path

    def run_filter(self):
        """Main method to run the filtering process"""
        print("🚀 Starting GCC Cause List Filter")
        print("=" * 60)
        print(f"📁 Input Directory: {self.input_dir.absolute()}")
        print(f"📁 Output Directory: {self.output_dir.absolute()}")
        print()
        
        # Process all PDFs
        self.process_all_pdfs()
        
        if not self.filtered_cases:
            print("❌ No GCC-related cases found in the cause lists")
            return False
        
        # Save results
        json_path = self.save_to_json()
        pdf_path = self.create_pdf_report()

        print(f"\n🎉 Filtering completed successfully!")
        print(f"📊 Found {len(self.filtered_cases)} GCC-related cases")
        print(f"💾 Results saved to:")
        print(f"   JSON: {json_path}")
        if pdf_path:
            print(f"   PDF:  {pdf_path}")

        return True


def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Filter GCC cases from cause list PDFs')
    parser.add_argument('--input-dir', type=str, help='Input directory containing PDFs', default='Cause list')
    parser.add_argument('--output-dir', type=str, help='Output directory for results', default='GCC_Filtered_Cases')
    
    args = parser.parse_args()
    
    # Create and run filter
    filter_tool = GCCCauseListFilter(
        input_dir=args.input_dir,
        output_dir=args.output_dir
    )
    
    print("📋 Configuration:")
    print(f"   Input Directory: {args.input_dir}")
    print(f"   Output Directory: {args.output_dir}")
    print()
    
    success = filter_tool.run_filter()
    
    if success:
        print("\n✅ Script completed successfully!")
    else:
        print("\n❌ Script failed or no cases found.")


if __name__ == "__main__":
    main()
