#!/usr/bin/env python3
"""
Madras High Court Cause List Scraper
Automates the process of downloading daily cause lists from https://mhc.tn.gov.in/judis/clists/clists-madras/index.php
"""

import os
import time
import requests
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from pathlib import Path

class CauseListScraper:
    def __init__(self, download_dir="Cause list", headless=False):
        """
        Initialize the cause list scraper
        
        Args:
            download_dir (str): Directory to save downloaded PDFs
            headless (bool): Run browser in headless mode
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.base_url = "https://mhc.tn.gov.in/judis/clists/clists-madras/index.php"
        self.driver = None
        self.headless = headless

    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")

        # Set download preferences
        prefs = {
            "download.default_directory": str(self.download_dir.absolute()),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "plugins.always_open_pdf_externally": True  # Download PDFs instead of opening in browser
        }
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            return True
        except Exception as e:
            print(f"Error setting up Chrome driver: {e}")
            print("Please ensure ChromeDriver is installed and in PATH")
            return False

    def wait_for_element(self, by, value, timeout=10):
        """Wait for element to be present and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            print(f"Timeout waiting for element: {by}={value}")
            return None

    def wait_for_clickable_element(self, by, value, timeout=10):
        """Wait for element to be clickable and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            print(f"Timeout waiting for clickable element: {by}={value}")
            return None

    def debug_page_elements(self, step_name):
        """Debug method to print page elements for troubleshooting"""
        try:
            print(f"\n🔍 DEBUG: Page elements for {step_name}")
            print(f"Current URL: {self.driver.current_url}")
            print(f"Page title: {self.driver.title}")

            # Check for radio buttons
            radio_buttons = self.driver.find_elements(By.CSS_SELECTOR, "input[type='radio']")
            print(f"Radio buttons found: {len(radio_buttons)}")
            for i, radio in enumerate(radio_buttons[:3]):  # Show first 3
                print(f"  Radio {i}: id='{radio.get_attribute('id')}', name='{radio.get_attribute('name')}', value='{radio.get_attribute('value')}'")

            # Check for select elements
            selects = self.driver.find_elements(By.TAG_NAME, "select")
            print(f"Select elements found: {len(selects)}")
            for i, select in enumerate(selects[:3]):  # Show first 3
                print(f"  Select {i}: id='{select.get_attribute('id')}', name='{select.get_attribute('name')}', class='{select.get_attribute('class')}'")

            # Check for buttons
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            print(f"Buttons found: {len(buttons)}")
            for i, button in enumerate(buttons[:3]):  # Show first 3
                print(f"  Button {i}: text='{button.text}', type='{button.get_attribute('type')}', class='{button.get_attribute('class')}'")

            print("🔍 DEBUG: End of page elements\n")

        except Exception as e:
            print(f"Debug error: {e}")
            pass

    def click_daily_list(self):
        """Step 1: Click on Daily List radio button"""
        print("Step 1: Clicking Daily List...")

        try:
            # Wait for the page to load
            time.sleep(3)

            # Debug: Show page elements
            self.debug_page_elements("Daily List Selection")

            # Find and click the Daily List radio button using the correct ID
            daily_list_radio = self.wait_for_clickable_element(By.ID, "dailylist")
            if not daily_list_radio:
                # Try alternative selectors based on the actual HTML structure
                selectors = [
                    (By.XPATH, "//input[@id='dailylist']"),
                    (By.XPATH, "//input[@name='clistgroup' and @value='1']"),
                    (By.CSS_SELECTOR, "input[id='dailylist']"),
                    (By.CSS_SELECTOR, "input[name='clistgroup'][value='1']"),
                    (By.XPATH, "//label[contains(text(), 'Daily List')]/preceding-sibling::input"),
                    (By.XPATH, "//label[@for='dailylist']/../input")
                ]

                for selector_type, selector_value in selectors:
                    try:
                        print(f"Trying selector: {selector_type}={selector_value}")
                        daily_list_radio = self.wait_for_clickable_element(selector_type, selector_value, timeout=5)
                        if daily_list_radio:
                            print(f"✓ Found Daily List radio button using: {selector_type}={selector_value}")
                            break
                    except Exception as e:
                        print(f"Selector failed: {e}")
                        continue

            if daily_list_radio:
                # Scroll to element and click
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", daily_list_radio)
                time.sleep(1)

                # Try different click methods
                try:
                    daily_list_radio.click()
                except Exception:
                    print("Regular click failed, trying JavaScript click...")
                    self.driver.execute_script("arguments[0].click();", daily_list_radio)

                time.sleep(2)
                print("✓ Daily List selected successfully")
                return True
            else:
                print("✗ Could not find Daily List radio button")
                return False

        except Exception as e:
            print(f"✗ Error clicking Daily List: {e}")
            return False

    def select_date(self, target_date=None):
        """Step 2: Select the date"""
        print("Step 2: Selecting date...")

        # Default to today if no date provided
        if not target_date:
            target_date = datetime.now().strftime("%d-%m-%Y")

        try:
            # Wait for the date selector to appear after clicking Daily List
            time.sleep(2)

            # Find the date dropdown/selector - try multiple selectors
            date_selectors = [
                (By.ID, "date_select"),
                (By.NAME, "date"),
                (By.CSS_SELECTOR, "select[name*='date']"),
                (By.CSS_SELECTOR, "select.form-control"),
                (By.XPATH, "//select[contains(@class, 'form-control')]"),
                (By.XPATH, "//select[option[contains(text(), 'Please Select')]]"),
                (By.CSS_SELECTOR, "select[name='clist_date']"),
                (By.ID, "clist_date")
            ]

            date_dropdown = None
            for selector_type, selector_value in date_selectors:
                try:
                    print(f"Trying date selector: {selector_type}={selector_value}")
                    date_dropdown = self.wait_for_element(selector_type, selector_value, timeout=5)
                    if date_dropdown:
                        print(f"✓ Found date dropdown using: {selector_type}={selector_value}")
                        break
                except Exception as e:
                    print(f"Date selector failed: {e}")
                    continue

            if date_dropdown:
                select_date = Select(date_dropdown)

                # Get all available options
                options = select_date.options
                print(f"📅 Available date options: {len(options)}")

                # Print first few options for debugging
                for i, option in enumerate(options[:5]):
                    print(f"   Option {i}: '{option.text}' (value: '{option.get_attribute('value')}')")

                # Try to find the target date in the options
                target_found = False

                for option in options:
                    option_text = option.text.strip()
                    option_value = option.get_attribute("value")

                    # Check if this option matches our target date
                    if target_date in option_text or target_date in option_value:
                        select_date.select_by_visible_text(option_text)
                        target_found = True
                        print(f"✓ Date selected: {option_text}")
                        break

                if not target_found:
                    # If exact date not found, select the first available date (skip "Please Select")
                    if len(options) > 1:
                        select_date.select_by_index(1)
                        selected_option = select_date.first_selected_option
                        print(f"⚠️ Target date {target_date} not found. Selected: {selected_option.text}")
                    else:
                        print("✗ No date options available")
                        return False

                time.sleep(2)
                return True
            else:
                print("✗ Could not find date selector")
                print("💡 The date selector might appear after selecting Daily List. Check if Daily List was selected properly.")
                return False

        except Exception as e:
            print(f"✗ Error selecting date: {e}")
            return False

    def click_next_button(self):
        """Step 3: Click the Next button"""
        print("Step 3: Clicking Next button...")

        try:
            # Try multiple selectors for the Next button
            next_selectors = [
                (By.XPATH, "//button[contains(text(), 'NEXT')]"),
                (By.XPATH, "//button[contains(text(), 'Next')]"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.XPATH, "//input[@value='NEXT']"),
                (By.XPATH, "//input[@value='Next']"),
                (By.CSS_SELECTOR, "button.btn-primary"),
                (By.CSS_SELECTOR, "input[type='submit']"),
                (By.XPATH, "//button[contains(@class, 'btn')]"),
                (By.XPATH, "//form//button"),
                (By.XPATH, "//form//input[@type='submit']")
            ]

            next_button = None
            for selector_type, selector_value in next_selectors:
                try:
                    print(f"Trying Next button selector: {selector_type}={selector_value}")
                    next_button = self.wait_for_clickable_element(selector_type, selector_value, timeout=5)
                    if next_button:
                        print(f"✓ Found Next button using: {selector_type}={selector_value}")
                        break
                except Exception as e:
                    print(f"Next button selector failed: {e}")
                    continue

            if next_button:
                # Scroll to element and click
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                time.sleep(1)

                # Try different click methods
                try:
                    next_button.click()
                except Exception:
                    print("Regular click failed, trying JavaScript click...")
                    self.driver.execute_script("arguments[0].click();", next_button)

                time.sleep(5)  # Wait longer for page to load
                print("✓ Next button clicked successfully")
                return True
            else:
                print("✗ Could not find Next button")
                print("💡 Make sure a date is selected before clicking Next")
                return False

        except Exception as e:
            print(f"✗ Error clicking Next button: {e}")
            return False

    def click_entire_causelist(self):
        """Step 4: Click on Entire Causelist tab"""
        print("Step 4: Clicking Entire Causelist...")
        
        try:
            # Wait for the causelist page to load
            time.sleep(3)
            
            entire_causelist_tab = self.wait_for_clickable_element(By.XPATH, "//a[contains(text(), 'Entire Causelist')]")
            if not entire_causelist_tab:
                # Try alternative selectors
                entire_causelist_tab = self.wait_for_clickable_element(By.XPATH, "//button[contains(text(), 'Entire Causelist')]")
                if not entire_causelist_tab:
                    entire_causelist_tab = self.wait_for_clickable_element(By.CSS_SELECTOR, "a[href*='entire'], button[onclick*='entire']")
            
            if entire_causelist_tab:
                entire_causelist_tab.click()
                time.sleep(2)
                print("✓ Entire Causelist tab clicked successfully")
                return True
            else:
                print("✗ Could not find Entire Causelist tab")
                return False
                
        except Exception as e:
            print(f"✗ Error clicking Entire Causelist: {e}")
            return False

    def download_causelist_pdf(self):
        """Step 5: Download the complete causelist PDF"""
        print("Step 5: Downloading causelist PDF...")
        
        try:
            # Wait for the tab content to load
            time.sleep(2)
            
            # Look for the PDF download button
            pdf_button = self.wait_for_clickable_element(By.XPATH, "//a[contains(text(), 'VIEW COMPLETE CAUSLIST PDF')]")
            if not pdf_button:
                # Try alternative selectors
                selectors = [
                    "//button[contains(text(), 'VIEW COMPLETE')]",
                    "//a[contains(text(), 'DOWNLOAD')]",
                    "//a[contains(text(), 'PDF')]",
                    "//button[contains(text(), 'PDF')]",
                    "//a[@href*='.pdf']",
                    "//a[contains(@onclick, 'pdf')]"
                ]
                
                for selector in selectors:
                    pdf_button = self.wait_for_clickable_element(By.XPATH, selector, timeout=5)
                    if pdf_button:
                        print(f"✓ Found PDF button using selector: {selector}")
                        break
            
            if pdf_button:
                # Get the current number of files in download directory
                initial_files = set(os.listdir(self.download_dir))
                
                pdf_button.click()
                print("✓ PDF download initiated...")
                
                # Wait for download to complete
                max_wait = 30  # seconds
                wait_count = 0
                
                while wait_count < max_wait:
                    current_files = set(os.listdir(self.download_dir))
                    new_files = current_files - initial_files
                    
                    # Check for completed downloads (not .crdownload files)
                    completed_files = [f for f in new_files if not f.endswith('.crdownload')]
                    
                    if completed_files:
                        for file in completed_files:
                            print(f"✓ Downloaded: {file}")
                        return True
                    
                    time.sleep(1)
                    wait_count += 1
                    
                    if wait_count % 5 == 0:
                        print(f"⏳ Still waiting for download... ({wait_count}s)")
                
                print("⚠️ Download timeout - file may still be downloading")
                return True  # Return True as download was initiated
                
            else:
                print("✗ Could not find PDF download button")
                return False
                
        except Exception as e:
            print(f"✗ Error downloading PDF: {e}")
            return False

    def run_scraper(self, target_date=None):
        """Main method to run the complete scraping process"""
        print("🚀 Starting Madras High Court Cause List Scraper")
        print("=" * 60)
        print(f"📅 Target Date: {target_date or 'Today'}")
        print(f"📁 Download Directory: {self.download_dir.absolute()}")
        print("🌐 A Chrome browser window will open - don't close it!")
        print()
        
        try:
            # Setup driver
            if not self.setup_driver():
                return False
            
            # Navigate to the website
            print(f"🌐 Navigating to: {self.base_url}")
            self.driver.get(self.base_url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Execute all steps
            steps = [
                self.click_daily_list,
                lambda: self.select_date(target_date),
                self.click_next_button,
                self.click_entire_causelist,
                self.download_causelist_pdf
            ]
            
            for i, step in enumerate(steps, 1):
                print(f"\n--- Step {i} ---")
                if not step():
                    print(f"❌ Scraping failed at step {i}")
                    return False
                time.sleep(1)
            
            print("\n🎉 Cause list scraping completed successfully!")
            print(f"📁 Check the '{self.download_dir}' folder for downloaded files")
            return True
            
        except Exception as e:
            print(f"❌ Unexpected error during scraping: {e}")
            return False
        finally:
            if self.driver:
                print("\n🔄 Closing browser...")
                time.sleep(2)  # Give time to see the final result
                self.driver.quit()

    def __del__(self):
        """Cleanup method"""
        if hasattr(self, 'driver') and self.driver:
            self.driver.quit()


def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Download cause lists from Madras High Court')
    parser.add_argument('--date', type=str, help='Target date (DD-MM-YYYY format)', default=None)
    parser.add_argument('--download-dir', type=str, help='Download directory', default='Cause list')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    
    args = parser.parse_args()
    
    # Validate date format if provided
    if args.date:
        try:
            datetime.strptime(args.date, '%d-%m-%Y')
        except ValueError:
            print("❌ Invalid date format. Use DD-MM-YYYY")
            return
    
    # Create and run scraper
    scraper = CauseListScraper(
        download_dir=args.download_dir,
        headless=args.headless
    )
    
    print("📋 Configuration:")
    print(f"   Target Date: {args.date or 'Today'}")
    print(f"   Download Directory: {args.download_dir}")
    print(f"   Headless Mode: {args.headless}")
    print()
    
    success = scraper.run_scraper(target_date=args.date)
    
    if success:
        print("\n✅ Script completed successfully!")
    else:
        print("\n❌ Script failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
