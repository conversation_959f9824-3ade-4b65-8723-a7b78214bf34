#!/usr/bin/env python3
"""
Madras High Court Cause List Scraper
Automates the process of downloading daily cause lists from https://mhc.tn.gov.in/judis/clists/clists-madras/index.php
"""

import os
import time
import requests
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from pathlib import Path

class CauseListScraper:
    def __init__(self, download_dir="Cause list", headless=False):
        """
        Initialize the cause list scraper
        
        Args:
            download_dir (str): Directory to save downloaded PDFs
            headless (bool): Run browser in headless mode
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.base_url = "https://mhc.tn.gov.in/judis/clists/clists-madras/index.php"
        self.driver = None
        self.headless = headless

    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")

        # Set download preferences
        prefs = {
            "download.default_directory": str(self.download_dir.absolute()),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "plugins.always_open_pdf_externally": True  # Download PDFs instead of opening in browser
        }
        chrome_options.add_experimental_option("prefs", prefs)
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            return True
        except Exception as e:
            print(f"Error setting up Chrome driver: {e}")
            print("Please ensure ChromeDriver is installed and in PATH")
            return False

    def wait_for_element(self, by, value, timeout=10):
        """Wait for element to be present and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            print(f"Timeout waiting for element: {by}={value}")
            return None

    def wait_for_clickable_element(self, by, value, timeout=10):
        """Wait for element to be clickable and return it"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            print(f"Timeout waiting for clickable element: {by}={value}")
            return None

    def click_daily_list(self):
        """Step 1: Click on Daily List radio button"""
        print("Step 1: Clicking Daily List...")
        
        try:
            # Wait for the page to load
            time.sleep(2)
            
            # Find and click the Daily List radio button
            daily_list_radio = self.wait_for_clickable_element(By.XPATH, "//input[@type='radio' and @value='DAILY LIST']")
            if not daily_list_radio:
                # Try alternative selector
                daily_list_radio = self.wait_for_clickable_element(By.XPATH, "//label[contains(text(), 'DAILY LIST')]/preceding-sibling::input")
            
            if daily_list_radio:
                daily_list_radio.click()
                time.sleep(1)
                print("✓ Daily List selected successfully")
                return True
            else:
                print("✗ Could not find Daily List radio button")
                return False
                
        except Exception as e:
            print(f"✗ Error clicking Daily List: {e}")
            return False

    def select_date(self, target_date=None):
        """Step 2: Select the date"""
        print("Step 2: Selecting date...")
        
        # Default to today if no date provided
        if not target_date:
            target_date = datetime.now().strftime("%d-%m-%Y")
        
        try:
            # Find the date dropdown/selector
            date_dropdown = self.wait_for_element(By.ID, "date_select")
            if not date_dropdown:
                # Try alternative selectors
                date_dropdown = self.wait_for_element(By.NAME, "date")
                if not date_dropdown:
                    date_dropdown = self.wait_for_element(By.CSS_SELECTOR, "select[name*='date']")
            
            if date_dropdown:
                select_date = Select(date_dropdown)
                
                # Try to find the target date in the options
                options = select_date.options
                target_found = False
                
                for option in options:
                    option_text = option.text.strip()
                    option_value = option.get_attribute("value")
                    
                    # Check if this option matches our target date
                    if target_date in option_text or target_date in option_value:
                        select_date.select_by_visible_text(option_text)
                        target_found = True
                        print(f"✓ Date selected: {option_text}")
                        break
                
                if not target_found:
                    # If exact date not found, select the first available date
                    if len(options) > 1:  # Skip the default "Please Select" option
                        select_date.select_by_index(1)
                        selected_option = select_date.first_selected_option
                        print(f"⚠️ Target date {target_date} not found. Selected: {selected_option.text}")
                    else:
                        print("✗ No date options available")
                        return False
                
                time.sleep(1)
                return True
            else:
                print("✗ Could not find date selector")
                return False
                
        except Exception as e:
            print(f"✗ Error selecting date: {e}")
            return False

    def click_next_button(self):
        """Step 3: Click the Next button"""
        print("Step 3: Clicking Next button...")
        
        try:
            next_button = self.wait_for_clickable_element(By.XPATH, "//button[contains(text(), 'NEXT')]")
            if not next_button:
                # Try alternative selectors
                next_button = self.wait_for_clickable_element(By.CSS_SELECTOR, "button[type='submit']")
                if not next_button:
                    next_button = self.wait_for_clickable_element(By.XPATH, "//input[@value='NEXT']")
            
            if next_button:
                next_button.click()
                time.sleep(3)  # Wait for page to load
                print("✓ Next button clicked successfully")
                return True
            else:
                print("✗ Could not find Next button")
                return False
                
        except Exception as e:
            print(f"✗ Error clicking Next button: {e}")
            return False

    def click_entire_causelist(self):
        """Step 4: Click on Entire Causelist tab"""
        print("Step 4: Clicking Entire Causelist...")
        
        try:
            # Wait for the causelist page to load
            time.sleep(3)
            
            entire_causelist_tab = self.wait_for_clickable_element(By.XPATH, "//a[contains(text(), 'Entire Causelist')]")
            if not entire_causelist_tab:
                # Try alternative selectors
                entire_causelist_tab = self.wait_for_clickable_element(By.XPATH, "//button[contains(text(), 'Entire Causelist')]")
                if not entire_causelist_tab:
                    entire_causelist_tab = self.wait_for_clickable_element(By.CSS_SELECTOR, "a[href*='entire'], button[onclick*='entire']")
            
            if entire_causelist_tab:
                entire_causelist_tab.click()
                time.sleep(2)
                print("✓ Entire Causelist tab clicked successfully")
                return True
            else:
                print("✗ Could not find Entire Causelist tab")
                return False
                
        except Exception as e:
            print(f"✗ Error clicking Entire Causelist: {e}")
            return False

    def download_causelist_pdf(self):
        """Step 5: Download the complete causelist PDF"""
        print("Step 5: Downloading causelist PDF...")
        
        try:
            # Wait for the tab content to load
            time.sleep(2)
            
            # Look for the PDF download button
            pdf_button = self.wait_for_clickable_element(By.XPATH, "//a[contains(text(), 'VIEW COMPLETE CAUSLIST PDF')]")
            if not pdf_button:
                # Try alternative selectors
                selectors = [
                    "//button[contains(text(), 'VIEW COMPLETE')]",
                    "//a[contains(text(), 'DOWNLOAD')]",
                    "//a[contains(text(), 'PDF')]",
                    "//button[contains(text(), 'PDF')]",
                    "//a[@href*='.pdf']",
                    "//a[contains(@onclick, 'pdf')]"
                ]
                
                for selector in selectors:
                    pdf_button = self.wait_for_clickable_element(By.XPATH, selector, timeout=5)
                    if pdf_button:
                        print(f"✓ Found PDF button using selector: {selector}")
                        break
            
            if pdf_button:
                # Get the current number of files in download directory
                initial_files = set(os.listdir(self.download_dir))
                
                pdf_button.click()
                print("✓ PDF download initiated...")
                
                # Wait for download to complete
                max_wait = 30  # seconds
                wait_count = 0
                
                while wait_count < max_wait:
                    current_files = set(os.listdir(self.download_dir))
                    new_files = current_files - initial_files
                    
                    # Check for completed downloads (not .crdownload files)
                    completed_files = [f for f in new_files if not f.endswith('.crdownload')]
                    
                    if completed_files:
                        for file in completed_files:
                            print(f"✓ Downloaded: {file}")
                        return True
                    
                    time.sleep(1)
                    wait_count += 1
                    
                    if wait_count % 5 == 0:
                        print(f"⏳ Still waiting for download... ({wait_count}s)")
                
                print("⚠️ Download timeout - file may still be downloading")
                return True  # Return True as download was initiated
                
            else:
                print("✗ Could not find PDF download button")
                return False
                
        except Exception as e:
            print(f"✗ Error downloading PDF: {e}")
            return False

    def run_scraper(self, target_date=None):
        """Main method to run the complete scraping process"""
        print("🚀 Starting Madras High Court Cause List Scraper")
        print("=" * 60)
        print(f"📅 Target Date: {target_date or 'Today'}")
        print(f"📁 Download Directory: {self.download_dir.absolute()}")
        print("🌐 A Chrome browser window will open - don't close it!")
        print()
        
        try:
            # Setup driver
            if not self.setup_driver():
                return False
            
            # Navigate to the website
            print(f"🌐 Navigating to: {self.base_url}")
            self.driver.get(self.base_url)
            
            # Wait for page to load
            time.sleep(3)
            
            # Execute all steps
            steps = [
                self.click_daily_list,
                lambda: self.select_date(target_date),
                self.click_next_button,
                self.click_entire_causelist,
                self.download_causelist_pdf
            ]
            
            for i, step in enumerate(steps, 1):
                print(f"\n--- Step {i} ---")
                if not step():
                    print(f"❌ Scraping failed at step {i}")
                    return False
                time.sleep(1)
            
            print("\n🎉 Cause list scraping completed successfully!")
            print(f"📁 Check the '{self.download_dir}' folder for downloaded files")
            return True
            
        except Exception as e:
            print(f"❌ Unexpected error during scraping: {e}")
            return False
        finally:
            if self.driver:
                print("\n🔄 Closing browser...")
                time.sleep(2)  # Give time to see the final result
                self.driver.quit()

    def __del__(self):
        """Cleanup method"""
        if hasattr(self, 'driver') and self.driver:
            self.driver.quit()


def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Download cause lists from Madras High Court')
    parser.add_argument('--date', type=str, help='Target date (DD-MM-YYYY format)', default=None)
    parser.add_argument('--download-dir', type=str, help='Download directory', default='Cause list')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    
    args = parser.parse_args()
    
    # Validate date format if provided
    if args.date:
        try:
            datetime.strptime(args.date, '%d-%m-%Y')
        except ValueError:
            print("❌ Invalid date format. Use DD-MM-YYYY")
            return
    
    # Create and run scraper
    scraper = CauseListScraper(
        download_dir=args.download_dir,
        headless=args.headless
    )
    
    print("📋 Configuration:")
    print(f"   Target Date: {args.date or 'Today'}")
    print(f"   Download Directory: {args.download_dir}")
    print(f"   Headless Mode: {args.headless}")
    print()
    
    success = scraper.run_scraper(target_date=args.date)
    
    if success:
        print("\n✅ Script completed successfully!")
    else:
        print("\n❌ Script failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
