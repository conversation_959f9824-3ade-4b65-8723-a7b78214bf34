import os
import re
import shutil
from PyPDF2 import PdfReader

# Configuration
download_dir = "downloads"
output_dir = "gcc_court_orders"

# Define case-types handled by GCC and related keywords/patterns
gcc_case_keywords = {
    "Arbitration Suits": [r"AS/"],
    "Civil Miscellaneous Applications": [r"CMA/"],
    "Contempt Petitions": [r"CONT P/", r"Contempt P"],
    "Criminal Original Petitions": [r"CRL OP/"],
    "Writ Petitions": [r"W\\.P\\.", r"WP/", r"Writ Petition"],
    "Original Applications": [r"OA/", r"Original Application"],
    "Civil Revision Petitions": [r"C\\.?R\\.?P\\.", r"CRP/", r"Civil Revision Petition"],
    "Civil Appeal": [r"C.A/", r"Civil Appeal"],
    "Land Acquisition Cases": [r"L.A.C/", r"Land Acquisition"],
    # Add more as needed
}

# General GCC-related terms
gcc_terms = [
    r"Greater\s+Chennai\s+Corporation",
    r"GCC(?![a-zA-Z])",
    r"Corporation\s+of\s+Chennai",
    r"Chennai\s+City\s+Municipal\s+Corporation",
    r"Chennai\s+Municipal\s+Corporation",
    r"Municipal\s+Corporation\s+Act",
    r"Tamil\s+Nadu\s+Town\s+and\s+Country\s+Planning\s+Act",
    r"Chennai\s+City\s+Municipal\s+Corporation\s+Act"

    # Core GCC identifiers
    r"Greater\s+Chennai\s+Corporation",
    r"\bGCC\b",  # More precise word boundary
    r"Corporation\s+of\s+Chennai",
    r"Chennai\s+City\s+Municipal\s+Corporation",
    r"Chennai\s+Municipal\s+Corporation",

    # GCC officials and departments
    r"Commissioner.*Chennai\s+Corporation",
    r"Chennai\s+Corporation.*Commissioner",
    r"Mayor.*Chennai",
    r"Deputy\s+Mayor.*Chennai",
    r"Zonal\s+Officer.*Chennai",
    r"Assistant\s+Commissioner.*Chennai",

    # GCC-specific locations and addresses
    r"Ripon\s+Building",  # GCC headquarters
    r"Corporation\s+Office.*Chennai",
    r"Chennai\s+Corporation\s+Office",

    # 1. URBAN PLANNING (GCC responsibility)
    r"(?:Urban\s+Planning|City\s+Planning|Master\s+Plan|Development\s+Plan).*Chennai",
    r"Chennai.*(?:Urban\s+Planning|City\s+Planning|Master\s+Plan|Development\s+Plan)",
    r"(?:Zoning|Land\s+Use|Layout\s+Approval).*Chennai",
    r"Chennai.*(?:Zoning|Land\s+Use|Layout\s+Approval)",

    # 2. ROAD MAINTENANCE (GCC responsibility)
    r"(?:Road\s+Maintenance|Road\s+Repair|Street\s+Repair|Pothole).*Chennai",
    r"Chennai.*(?:Road\s+Maintenance|Road\s+Repair|Street\s+Repair|Pothole)",
    r"(?:Footpath|Pavement|Sidewalk).*Chennai",
    r"Chennai.*(?:Footpath|Pavement|Sidewalk)",

    # 3. WATER SUPPLY (GCC responsibility)
    r"(?:Water\s+Supply|Water\s+Connection|Water\s+Board|Metro\s+Water).*Chennai",
    r"Chennai.*(?:Water\s+Supply|Water\s+Connection|Water\s+Board|Metro\s+Water)",
    r"(?:Drinking\s+Water|Potable\s+Water|Water\s+Quality).*Chennai",
    r"Chennai.*(?:Drinking\s+Water|Potable\s+Water|Water\s+Quality)",

    # 4. SANITATION (GCC responsibility)
    r"(?:Sanitation|Sewage|Drainage|Storm\s+Water).*Chennai",
    r"Chennai.*(?:Sanitation|Sewage|Drainage|Storm\s+Water)",
    r"(?:Toilet|Public\s+Convenience|Latrine).*Chennai",
    r"Chennai.*(?:Toilet|Public\s+Convenience|Latrine)",

    # 5. WASTE MANAGEMENT (GCC responsibility)
    r"(?:Waste\s+Management|Garbage|Solid\s+Waste|Refuse).*Chennai",
    r"Chennai.*(?:Waste\s+Management|Garbage|Solid\s+Waste|Refuse)",
    r"(?:Waste\s+Collection|Waste\s+Disposal|Landfill|Dumping).*Chennai",
    r"Chennai.*(?:Waste\s+Collection|Waste\s+Disposal|Landfill|Dumping)",

    # 6. PUBLIC HEALTH (GCC responsibility)
    r"(?:Public\s+Health|Health\s+Department|Medical\s+Officer).*Chennai",
    r"Chennai.*(?:Public\s+Health|Health\s+Department|Medical\s+Officer)",
    r"(?:Epidemic|Disease\s+Control|Vector\s+Control|Mosquito).*Chennai",
    r"Chennai.*(?:Epidemic|Disease\s+Control|Vector\s+Control|Mosquito)",

    # 7. STREET LIGHTING (GCC responsibility)
    r"(?:Street\s+Light|Street\s+Lamp|Public\s+Lighting).*Chennai",
    r"Chennai.*(?:Street\s+Light|Street\s+Lamp|Public\s+Lighting)",
    r"(?:LED\s+Light|Sodium\s+Light|Electric\s+Post).*Chennai",
    r"Chennai.*(?:LED\s+Light|Sodium\s+Light|Electric\s+Post)",

    # 8. PARKS AND RECREATION (GCC responsibility) - More specific patterns
    r"(?:Public\s+Park|Municipal\s+Park|Corporation\s+Park|Playground|Recreation\s+Center).*Chennai",
    r"Chennai.*(?:Public\s+Park|Municipal\s+Park|Corporation\s+Park|Playground|Recreation\s+Center)",
    r"(?:Green\s+Space|Open\s+Space|Public\s+Garden|Children\s+Park).*Chennai\s+Corporation",
    r"Chennai\s+Corporation.*(?:Green\s+Space|Open\s+Space|Public\s+Garden|Children\s+Park)",
    r"(?:Park\s+Maintenance|Garden\s+Maintenance).*Chennai",
    r"Chennai.*(?:Park\s+Maintenance|Garden\s+Maintenance)",

    # 9. BUILDING REGULATION (GCC responsibility)
    r"(?:Building\s+Permit|Building\s+Approval|Construction\s+Permit).*Chennai",
    r"Chennai.*(?:Building\s+Permit|Building\s+Approval|Construction\s+Permit)",
    r"(?:Building\s+Plan|Occupancy\s+Certificate|Completion\s+Certificate).*Chennai",
    r"Chennai.*(?:Building\s+Plan|Occupancy\s+Certificate|Completion\s+Certificate)",
    r"(?:Unauthorized\s+Construction|Illegal\s+Construction|Demolition).*Chennai",
    r"Chennai.*(?:Unauthorized\s+Construction|Illegal\s+Construction|Demolition)",

    # 10. TAXATION AND REVENUE (GCC responsibility)
    r"(?:Property\s+Tax|House\s+Tax|Assessment|Revenue).*Chennai",
    r"Chennai.*(?:Property\s+Tax|House\s+Tax|Assessment|Revenue)",
    r"(?:Trade\s+License|Professional\s+Tax|Advertisement\s+Tax).*Chennai",
    r"Chennai.*(?:Trade\s+License|Professional\s+Tax|Advertisement\s+Tax)",

    # 11. MARKET AND COMMERCIAL (GCC responsibility)
    r"(?:Market|Commercial\s+Complex|Shopping\s+Complex).*Chennai",
    r"Chennai.*(?:Market|Commercial\s+Complex|Shopping\s+Complex)",
    r"(?:Vendor|Hawker|Street\s+Vendor).*Chennai",
    r"Chennai.*(?:Vendor|Hawker|Street\s+Vendor)",

    # 12. WARD AND ADMINISTRATIVE (GCC responsibility)
    r"Ward.*Chennai",
    r"Chennai.*Ward",
    r"(?:Zone|Division|Circle).*Chennai\s+Corporation",
    r"Chennai\s+Corporation.*(?:Zone|Division|Circle)",
    r"(?:Councillor|Corporator).*Chennai",
    r"Chennai.*(?:Councillor|Corporator)",

    # 13. LEGAL ACTS AND REGULATIONS
    r"Chennai\s+City\s+Municipal\s+Corporation\s+Act",
    r"Tamil\s+Nadu\s+District\s+Municipalities\s+Act.*Chennai",
    r"(?:Municipal|Corporation).*(?:Act|Rules|Regulations).*Chennai",
    r"Chennai.*(?:Municipal|Corporation).*(?:Act|Rules|Regulations)",

    # 14. GENERAL MUNICIPAL SERVICES IN CHENNAI
    r"(?:Municipal\s+Services|Civic\s+Services|Public\s+Services).*Chennai",
    r"Chennai.*(?:Municipal\s+Services|Civic\s+Services|Public\s+Services)",
    r"(?:Birth\s+Certificate|Death\s+Certificate|Marriage\s+Certificate).*Chennai",
    r"Chennai.*(?:Birth\s+Certificate|Death\s+Certificate|Marriage\s+Certificate)",
]

# Flatten all patterns
patterns = []
for kws in gcc_case_keywords.values():
    patterns.extend(kws)
patterns.extend(gcc_terms)

# Compile combined regex
combined_pattern = re.compile("|".join(f"({p})" for p in patterns), re.IGNORECASE)

# Prepare output directory
os.makedirs(output_dir, exist_ok=True)

matched_files = []
total_files = 0

# Process each PDF in download_dir
for fname in os.listdir(download_dir):
    if not fname.lower().endswith('.pdf'):
        continue
    total_files += 1
    src = os.path.join(download_dir, fname)
    try:
        reader = PdfReader(src)
        text_content = []
        for page in reader.pages:
            txt = page.extract_text() or ''
            text_content.append(txt)
        full_text = '\n'.join(text_content)

        # If any GCC-related term or case-type keyword matches, copy file
        if combined_pattern.search(full_text):
            dest = os.path.join(output_dir, fname)
            shutil.copy2(src, dest)
            matched_files.append(fname)
    except Exception as e:
        print(f"Error reading {fname}: {e}")

# Summary output
print(f"Scanned {total_files} PDFs.")
print(f"Identified {len(matched_files)} files matching GCC criteria.")
print("Copied to folder:", output_dir)
for f in matched_files:
    print(f" - {f}")
